import { useQuery } from '@tanstack/react-query';
import { useSession } from '@/modules/login/auth-provider';
import { apiClient } from '@/data/api-client';
import type { ClassesQueryParams } from '@/data/api-client/classes';

export const useClassesQuery = (params: Omit<ClassesQueryParams, 'orgId'>) => {
  const { data: session } = useSession();

  const queryParams: ClassesQueryParams = {
    ...params,
    orgId: session?.university_id as string,
  };

  return useQuery({
    queryKey: ['classes', queryParams],
    queryFn: () => apiClient.getClassesByOrgId(queryParams),
    enabled: !!session?.university_id,
  });
};
