import { useMutation, useQuery } from '@tanstack/react-query';
import { useSession } from '@/modules/login/auth-provider';
import { apiClient } from '@/data/api-client';
import { RequestInfoPayload } from '@/data/api-client/trainer';
import { ToastManager } from '@/components/shared/toast/ToastManager';

export const useTrainersForRequestInfo = () => {
  const { data: sessionData } = useSession();

  return useQuery({
    queryKey: ['trainers-request-info', sessionData?.university_id],
    queryFn: () =>
      apiClient.getTrainersForRequestInfo({
        universityId: sessionData?.university_id as string,
      }),
    enabled: !!sessionData?.university_id,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
};

export const useSubmitRequestInfo = () => {
  return useMutation({
    mutationFn: (payload: RequestInfoPayload) =>
      apiClient.submitRequestInfo(payload),
    onSuccess: data => {
      ToastManager.show(data.message, 'success');
    },
    onError: error => {
      ToastManager.show(
        error instanceof Error
          ? error.message
          : 'Failed to submit request info',
        'error'
      );
    },
  });
};
