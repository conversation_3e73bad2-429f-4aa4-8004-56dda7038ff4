import { useMutation, useQuery } from '@tanstack/react-query';
import { useSession } from '@/modules/login/auth-provider';
import { apiClient } from '@/data/api-client';
import { RequestInfoPayload } from '@/data/api-client/trainer';

export const useTrainersForRequestInfo = () => {
  const { data: sessionData } = useSession();

  return useQuery({
    queryKey: ['trainers-request-info', sessionData?.university_id],
    queryFn: () =>
      apiClient.getTrainersForRequestInfo({
        universityId: sessionData?.university_id as string,
      }),
    enabled: !!sessionData?.university_id,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
};

export const useSubmitRequestInfo = () => {
  return useMutation({
    mutationFn: (payload: RequestInfoPayload) =>
      apiClient.submitRequestInfo(payload),
    onSuccess: data => {
      console.log('Request info submitted successfully:', data);
      // The success toast will be shown by the form's onSubmit handler
    },
    onError: error => {
      console.error('Request info submission failed:', error);
      // The error toast will be shown by the form's onSubmit handler
    },
  });
};
