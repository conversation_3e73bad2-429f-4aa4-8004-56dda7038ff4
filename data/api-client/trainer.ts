/* eslint-disable @typescript-eslint/no-unused-vars */
import { api } from '@/lib/api';
import { Trainer } from '../screens/appointments/types';

export interface RequestInfoPayload {
  first_name: string;
  last_name: string;
  email: string;
  phone_number: string;
  gym_id: string;
  category_id: string;
  session_id: string;
  training_days: string;
  training_per_week: string;
  availability: string;
  focus_area: string;
  gender_pref: string;
  university_id: string;
  requested_trainer_id: string;
}

export interface RequestInfoResponse {
  success: boolean;
  message: string;
}

export const fetchTrainersBySessionId = async (id: string) => {
  try {
    const response = await api
      .get<{
        data: Trainer[];
      }>(`users/trainers?session_id=${id}`)
      .json();

    return response.data;
  } catch (err) {
    throw new Error('Could not fetch appointments');
  }
};

export const fetchTrainersByOrgId = async ({ orgId }: { orgId: string }) => {
  try {
    const urlParams = new URLSearchParams({
      university_id: orgId,
      per_page: '600',
    }).toString();

    const response = await api
      .get<{
        trainers: Trainer[];
      }>(`trainers/unauth?${urlParams}`)
      .json();

    return response.trainers;
  } catch (err) {
    throw new Error('Could not fetch appointments');
  }
};

export const fetchTrainersForRequestInfo = async ({
  universityId,
}: {
  universityId: string;
}) => {
  try {
    const urlParams = new URLSearchParams({
      university_id: universityId,
    }).toString();

    const response = await api
      .get<
        | {
            trainers?: Trainer[];
          }
        | Trainer[]
      >(`trainers/unauth?${urlParams}`)
      .json();

    // Handle both response formats - array directly or wrapped in trainers property
    if (Array.isArray(response)) {
      return response;
    } else {
      return response.trainers || [];
    }
  } catch (err) {
    throw new Error('Could not fetch trainers');
  }
};

export const submitRequestInfo = async (
  payload: RequestInfoPayload
): Promise<RequestInfoResponse> => {
  try {
    const response = await api
      .post<RequestInfoResponse>('trainers/requestinfo/unauth', {
        json: payload,
      })
      .json();

    return response;
  } catch (err) {
    throw new Error('Could not submit request info');
  }
};
