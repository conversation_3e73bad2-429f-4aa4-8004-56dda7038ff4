/* eslint-disable @typescript-eslint/no-unused-vars */
import { api } from '@/lib/api';
import { ClassDetailsResponse } from '../screens/classes/types';

export interface ClassesQueryParams {
  orgId: string;
  date: string;
  gym_id?: string;
  instructor_id?: string;
  class_type?: string[];
  start_time?: string;
  end_time?: string;
  only_available_reservations?: boolean;
}

export const fetchClassesByOrgId = async (params: ClassesQueryParams) => {
  try {
    const {
      orgId,
      date,
      gym_id,
      instructor_id,
      class_type,
      start_time,
      end_time,
      only_available_reservations,
    } = params;

    const urlParams = new URLSearchParams({
      university_id: orgId,
      date,
    });

    // Add optional parameters only if they have values
    if (gym_id) urlParams.append('gym_id', gym_id);
    if (instructor_id) urlParams.append('instructor_id', instructor_id);
    if (class_type && class_type.length > 0) {
      class_type.forEach(type => urlParams.append('class_type[]', type));
    }
    if (start_time) urlParams.append('start_time', start_time);
    if (end_time) urlParams.append('end_time', end_time);
    if (only_available_reservations) {
      urlParams.append('only_available_reservations', 'true');
    }

    const response = await api
      .get<{
        classes: ClassDetailsResponse[];
      }>(
        `classes/reservations/list?${urlParams.toString()}&exclude_if_closed_or_cancelled=true&exclude_past_classes=true`
      )
      .json();

    return response?.classes;
  } catch (err) {
    throw new Error('Could not fetch classes');
  }
};
