import React from 'react';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Button, ButtonText } from '@/components/ui/button';
import { SafeAreaView } from 'react-native-safe-area-context';
import { ScrollView } from 'react-native';
import { Pressable } from '@/components/ui/pressable';
import { Icon } from '@/components/ui/icon';
import { ArrowLeft } from 'lucide-react-native';
import { router } from 'expo-router';
import { useForm } from '@tanstack/react-form';
import { zodValidator } from '@tanstack/zod-form-adapter';
import {
  getInitials,
  getRandomColorForInitials,
} from '@/data/common/common.utils';
import {
  Radio,
  RadioGroup,
  RadioIndicator,
  RadioLabel,
} from '@/components/ui/radio';
import {
  Checkbox,
  CheckboxIndicator,
  CheckboxLabel,
  CheckboxIcon,
} from '@/components/ui/checkbox';
import { Check, CheckCircle } from 'lucide-react-native';
import {
  Avatar,
  AvatarImage,
  AvatarFallbackText,
} from '@/components/ui/avatar';
import { Input, InputField } from '@/components/ui/input';
import {
  FormControl,
  FormControlLabel,
  FormControlLabelText,
  FormControlError,
  FormControlErrorText,
} from '@/components/ui/form-control';
import {
  useTrainersForRequestInfo,
  useSubmitRequestInfo,
} from '@/data/screens/trainers/queries/useRequestInfo';
import { useSession } from '@/modules/login/auth-provider';
import { z } from 'zod';
import { useUIStore } from '@/stores/ui-store';

// Validation schema for request info form
const requestInfoSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  email: z
    .string()
    .min(1, 'Email is required')
    .email('Please enter a valid email address'),
  phoneNumber: z.string().min(1, 'Phone number is required'),
  trainingDays: z
    .array(z.string())
    .min(1, 'Please select at least one training day'),
  timesPerWeek: z.string().min(1, 'Please select training frequency'),
  availableTimes: z
    .array(z.string())
    .min(1, 'Please select at least one available time'),
  focusAreas: z
    .array(z.string())
    .min(1, 'Please select at least one focus area'),
  gender: z.string().min(1, 'Please select gender preference'),
  preferredTrainer: z.string().min(1, 'Please select a trainer preference'),
});

const RequestInfoHeader = () => {
  return (
    <HStack className="items-center px-4 py-4" space="md">
      <Pressable
        onPress={() => router.back()}
        className="w-10 h-10 items-center justify-center"
      >
        <Icon as={ArrowLeft} size="lg" className="text-typography-900" />
      </Pressable>

      <Text className="text-lg font-dm-sans-bold text-typography-900 flex-1">
        Request Info
      </Text>
    </HStack>
  );
};

const RequestInfo = () => {
  const { data: trainers, isLoading: trainersLoading } =
    useTrainersForRequestInfo();
  const { data: sessionData } = useSession();
  const submitRequestInfo = useSubmitRequestInfo();
  const { showToast } = useUIStore();

  const form = useForm({
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      phoneNumber: '',
      trainingDays: [] as string[],
      timesPerWeek: '',
      availableTimes: [] as string[],
      focusAreas: [] as string[],
      gender: '',
      preferredTrainer: '',
    },
    validatorAdapter: zodValidator(),
    validators: {
      onChange: requestInfoSchema,
    },
    onSubmit: async ({ value }) => {
      try {
        // Transform form data to match API payload format
        const payload = {
          first_name: value.firstName,
          last_name: value.lastName,
          email: value.email,
          phone_number: value.phoneNumber,
          gym_id: sessionData?.gym_id?.toString() || '27', // Default gym_id from sample
          category_id: '190', // Default category_id from sample
          session_id: '3884', // Default session_id from sample
          training_days: value.trainingDays.join(','),
          training_per_week: value.timesPerWeek,
          availability: value.availableTimes.join(','),
          focus_area: value.focusAreas.join(','),
          gender_pref: value.gender,
          university_id: sessionData?.university_id?.toString() || '33',
          requested_trainer_id:
            value.preferredTrainer === 'anyone' ? '' : value.preferredTrainer,
        };

        const result = await submitRequestInfo.mutateAsync(payload);
        showToast(
          result.message || 'Request info submitted successfully!',
          'success'
        );
        router.back();
      } catch (error) {
        console.error('Failed to submit request info:', error);
        const errorMessage =
          error instanceof Error
            ? error.message
            : 'Failed to submit request info. Please try again.';
        showToast(errorMessage, 'error');
      }
    },
  });

  const handleDayToggle = (day: string, currentDays: string[]) => {
    if (currentDays.includes(day)) {
      return currentDays.filter(d => d !== day);
    } else {
      return [...currentDays, day];
    }
  };

  const handleTimeToggle = (time: string, currentTimes: string[]) => {
    if (currentTimes.includes(time)) {
      return currentTimes.filter(t => t !== time);
    } else {
      return [...currentTimes, time];
    }
  };

  const handleFocusToggle = (focus: string, currentFocus: string[]) => {
    if (currentFocus.includes(focus)) {
      return currentFocus.filter(f => f !== focus);
    } else {
      return [...currentFocus, focus];
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <SafeAreaView className="flex-1 bg-background-0">
      <RequestInfoHeader />

      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        <VStack className="px-4 pb-6" space="lg">
          {/* Description */}
          <Text className="text-sm font-dm-sans-regular text-typography-600 leading-5">
            Please fill out the information below and we will get back to you.
          </Text>

          {/* Personal Information */}
          <VStack space="md">
            <Text className="text-lg font-dm-sans-bold text-typography-900">
              Personal Information
            </Text>

            {/* First Name */}
            <form.Field name="firstName">
              {field => (
                <FormControl>
                  <FormControlLabel>
                    <FormControlLabelText className="text-base font-dm-sans-medium text-typography-900">
                      First Name *
                    </FormControlLabelText>
                  </FormControlLabel>
                  <Input
                    variant="outline"
                    size="md"
                    className="border border-background-300 rounded-lg"
                  >
                    <InputField
                      placeholder="Enter your first name"
                      value={field.state.value}
                      onChangeText={field.handleChange}
                      onBlur={field.handleBlur}
                      className="text-typography-900"
                    />
                  </Input>
                  {field.state.meta.errors && (
                    <FormControlError>
                      <FormControlErrorText>
                        {field.state.meta.errors[0]}
                      </FormControlErrorText>
                    </FormControlError>
                  )}
                </FormControl>
              )}
            </form.Field>

            {/* Last Name */}
            <form.Field name="lastName">
              {field => (
                <FormControl>
                  <FormControlLabel>
                    <FormControlLabelText className="text-base font-dm-sans-medium text-typography-900">
                      Last Name *
                    </FormControlLabelText>
                  </FormControlLabel>
                  <Input
                    variant="outline"
                    size="md"
                    className="border border-background-300 rounded-lg"
                  >
                    <InputField
                      placeholder="Enter your last name"
                      value={field.state.value}
                      onChangeText={field.handleChange}
                      onBlur={field.handleBlur}
                      className="text-typography-900"
                    />
                  </Input>
                  {field.state.meta.errors && (
                    <FormControlError>
                      <FormControlErrorText>
                        {field.state.meta.errors[0]}
                      </FormControlErrorText>
                    </FormControlError>
                  )}
                </FormControl>
              )}
            </form.Field>

            {/* Email */}
            <form.Field name="email">
              {field => (
                <FormControl>
                  <FormControlLabel>
                    <FormControlLabelText className="text-base font-dm-sans-medium text-typography-900">
                      Email *
                    </FormControlLabelText>
                  </FormControlLabel>
                  <Input
                    variant="outline"
                    size="md"
                    className="border border-background-300 rounded-lg"
                  >
                    <InputField
                      placeholder="Enter your email address"
                      value={field.state.value}
                      onChangeText={field.handleChange}
                      onBlur={field.handleBlur}
                      keyboardType="email-address"
                      autoCapitalize="none"
                      className="text-typography-900"
                    />
                  </Input>
                  {field.state.meta.errors && (
                    <FormControlError>
                      <FormControlErrorText>
                        {field.state.meta.errors[0]}
                      </FormControlErrorText>
                    </FormControlError>
                  )}
                </FormControl>
              )}
            </form.Field>

            {/* Phone Number */}
            <form.Field name="phoneNumber">
              {field => (
                <FormControl>
                  <FormControlLabel>
                    <FormControlLabelText className="text-base font-dm-sans-medium text-typography-900">
                      Phone Number *
                    </FormControlLabelText>
                  </FormControlLabel>
                  <Input
                    variant="outline"
                    size="md"
                    className="border border-background-300 rounded-lg"
                  >
                    <InputField
                      placeholder="Enter your phone number"
                      value={field.state.value}
                      onChangeText={field.handleChange}
                      onBlur={field.handleBlur}
                      keyboardType="phone-pad"
                      className="text-typography-900"
                    />
                  </Input>
                  {field.state.meta.errors && (
                    <FormControlError>
                      <FormControlErrorText>
                        {field.state.meta.errors[0]}
                      </FormControlErrorText>
                    </FormControlError>
                  )}
                </FormControl>
              )}
            </form.Field>
          </VStack>

          {/* Training Days */}
          <VStack space="md">
            <Text className="text-base font-dm-sans-medium text-typography-900">
              What days per week are you looking to train?
            </Text>

            <form.Field name="trainingDays">
              {field => (
                <VStack space="sm">
                  <HStack space="sm" className="flex-wrap">
                    {['Monday', 'Tuesday'].map(day => (
                      <Pressable
                        key={day}
                        onPress={() =>
                          field.handleChange(
                            handleDayToggle(day, field.state.value)
                          )
                        }
                        className={`px-4 py-3 rounded-full border ${
                          field.state.value.includes(day)
                            ? 'border-[#00BFE0]'
                            : 'bg-background-50 border-background-300'
                        }`}
                        style={
                          field.state.value.includes(day)
                            ? { backgroundColor: '#00BFE0' }
                            : {}
                        }
                      >
                        <HStack className="items-center" space="xs">
                          <Text
                            className={`font-dm-sans-medium ${
                              field.state.value.includes(day)
                                ? 'text-white'
                                : 'text-typography-700'
                            }`}
                          >
                            {day}
                          </Text>
                          {field.state.value.includes(day) && (
                            <CheckCircle size={16} color="white" />
                          )}
                        </HStack>
                      </Pressable>
                    ))}
                  </HStack>

                  <HStack space="sm" className="flex-wrap">
                    {['Wednesday', 'Thursday'].map(day => (
                      <Pressable
                        key={day}
                        onPress={() =>
                          field.handleChange(
                            handleDayToggle(day, field.state.value)
                          )
                        }
                        className={`px-4 py-3 rounded-full border ${
                          field.state.value.includes(day)
                            ? 'border-[#00BFE0]'
                            : 'bg-background-50 border-background-300'
                        }`}
                        style={
                          field.state.value.includes(day)
                            ? { backgroundColor: '#00BFE0' }
                            : {}
                        }
                      >
                        <HStack className="items-center" space="xs">
                          <Text
                            className={`font-dm-sans-medium ${
                              field.state.value.includes(day)
                                ? 'text-white'
                                : 'text-typography-700'
                            }`}
                          >
                            {day}
                          </Text>
                          {field.state.value.includes(day) && (
                            <CheckCircle size={16} color="white" />
                          )}
                        </HStack>
                      </Pressable>
                    ))}
                  </HStack>

                  <HStack space="sm" className="flex-wrap">
                    {['Friday', 'Saturday'].map(day => (
                      <Pressable
                        key={day}
                        onPress={() =>
                          field.handleChange(
                            handleDayToggle(day, field.state.value)
                          )
                        }
                        className={`px-4 py-3 rounded-full border ${
                          field.state.value.includes(day)
                            ? 'border-[#00BFE0]'
                            : 'bg-background-50 border-background-300'
                        }`}
                        style={
                          field.state.value.includes(day)
                            ? { backgroundColor: '#00BFE0' }
                            : {}
                        }
                      >
                        <HStack className="items-center" space="xs">
                          <Text
                            className={`font-dm-sans-medium ${
                              field.state.value.includes(day)
                                ? 'text-white'
                                : 'text-typography-700'
                            }`}
                          >
                            {day}
                          </Text>
                          {field.state.value.includes(day) && (
                            <CheckCircle size={16} color="white" />
                          )}
                        </HStack>
                      </Pressable>
                    ))}
                  </HStack>

                  <Pressable
                    onPress={() =>
                      field.handleChange(
                        handleDayToggle('Sunday', field.state.value)
                      )
                    }
                    className={`px-4 py-3 rounded-full border self-start ${
                      field.state.value.includes('Sunday')
                        ? 'border-[#00BFE0]'
                        : 'bg-background-50 border-background-300'
                    }`}
                    style={
                      field.state.value.includes('Sunday')
                        ? { backgroundColor: '#00BFE0' }
                        : {}
                    }
                  >
                    <HStack className="items-center" space="xs">
                      <Text
                        className={`font-dm-sans-medium ${
                          field.state.value.includes('Sunday')
                            ? 'text-white'
                            : 'text-typography-700'
                        }`}
                      >
                        Sunday
                      </Text>
                      {field.state.value.includes('Sunday') && (
                        <CheckCircle size={16} color="white" />
                      )}
                    </HStack>
                  </Pressable>
                </VStack>
              )}
            </form.Field>
          </VStack>

          {/* Times per week */}
          <VStack space="md">
            <Text className="text-base font-dm-sans-medium text-typography-900">
              How many times per week are you looking to train?
            </Text>

            <form.Field name="timesPerWeek">
              {field => (
                <VStack space="sm">
                  <HStack space="sm" className="flex-wrap">
                    {['One', 'Two', 'Three', 'Four'].map(time => (
                      <Pressable
                        key={time}
                        onPress={() => field.handleChange(time)}
                        className={`px-4 py-3 rounded-full border ${
                          field.state.value === time
                            ? 'border-[#00BFE0]'
                            : 'bg-background-50 border-background-300'
                        }`}
                        style={
                          field.state.value === time
                            ? { backgroundColor: '#00BFE0' }
                            : {}
                        }
                      >
                        <HStack className="items-center" space="xs">
                          <Text
                            className={`font-dm-sans-medium ${
                              field.state.value === time
                                ? 'text-white'
                                : 'text-typography-700'
                            }`}
                          >
                            {time}
                          </Text>
                          {field.state.value === time && (
                            <CheckCircle size={16} color="white" />
                          )}
                        </HStack>
                      </Pressable>
                    ))}
                  </HStack>

                  <HStack space="sm" className="flex-wrap">
                    {['Five', 'Six'].map(time => (
                      <Pressable
                        key={time}
                        onPress={() => field.handleChange(time)}
                        className={`px-4 py-3 rounded-full border ${
                          field.state.value === time
                            ? 'border-[#00BFE0]'
                            : 'bg-background-50 border-background-300'
                        }`}
                        style={
                          field.state.value === time
                            ? { backgroundColor: '#00BFE0' }
                            : {}
                        }
                      >
                        <HStack className="items-center" space="xs">
                          <Text
                            className={`font-dm-sans-medium ${
                              field.state.value === time
                                ? 'text-white'
                                : 'text-typography-700'
                            }`}
                          >
                            {time}
                          </Text>
                          {field.state.value === time && (
                            <CheckCircle size={16} color="white" />
                          )}
                        </HStack>
                      </Pressable>
                    ))}
                  </HStack>
                </VStack>
              )}
            </form.Field>
          </VStack>

          {/* Available Times */}
          <VStack space="md">
            <Text className="text-base font-dm-sans-medium text-typography-900">
              What times are you available to train?
            </Text>

            <form.Field name="availableTimes">
              {field => (
                <HStack space="sm" className="flex-wrap">
                  {[
                    { key: 'morning', label: 'Morning', icon: '🌅' },
                    { key: 'afternoon', label: 'Afternoon', icon: '☀️' },
                    { key: 'evening', label: 'Evening', icon: '🌙' },
                  ].map(timeSlot => (
                    <Pressable
                      key={timeSlot.key}
                      onPress={() =>
                        field.handleChange(
                          handleTimeToggle(timeSlot.key, field.state.value)
                        )
                      }
                      className={`px-4 py-3 rounded-2xl border flex-row items-center ${
                        field.state.value.includes(timeSlot.key)
                          ? 'border-[#00BFE0]'
                          : 'bg-background-50 border-background-300'
                      }`}
                      style={
                        field.state.value.includes(timeSlot.key)
                          ? { backgroundColor: '#00BFE0' }
                          : {}
                      }
                    >
                      <Text className="text-lg mr-2">{timeSlot.icon}</Text>
                      <Text
                        className={`font-dm-sans-medium flex-1 ${
                          field.state.value.includes(timeSlot.key)
                            ? 'text-white'
                            : 'text-typography-700'
                        }`}
                      >
                        {timeSlot.label}
                      </Text>
                      {field.state.value.includes(timeSlot.key) && (
                        <CheckCircle size={16} color="white" className="ml-2" />
                      )}
                    </Pressable>
                  ))}
                </HStack>
              )}
            </form.Field>
          </VStack>

          {/* Focus Areas */}
          <VStack space="md">
            <Text className="text-base font-dm-sans-medium text-typography-900">
              What do you want to focus on?
            </Text>

            <form.Field name="focusAreas">
              {field => (
                <VStack space="sm">
                  {[
                    'Weight management',
                    'Sports conditioning',
                    'Strength training',
                    'Marathon coaching',
                  ].map(focus => (
                    <Checkbox
                      key={focus}
                      value={focus}
                      isChecked={field.state.value.includes(focus)}
                      onChange={() =>
                        field.handleChange(
                          handleFocusToggle(focus, field.state.value)
                        )
                      }
                      className="items-start"
                    >
                      <CheckboxIndicator className="mr-3 mt-0.5">
                        <CheckboxIcon as={Check} />
                      </CheckboxIndicator>
                      <CheckboxLabel className="flex-1">
                        <Text className="text-base font-dm-sans-regular text-typography-900">
                          {focus}
                        </Text>
                      </CheckboxLabel>
                    </Checkbox>
                  ))}
                </VStack>
              )}
            </form.Field>
          </VStack>

          {/* Gender */}
          <VStack space="md">
            <Text className="text-base font-dm-sans-medium text-typography-900">
              Gender
            </Text>

            <form.Field name="gender">
              {field => (
                <HStack space="sm" className="flex-wrap">
                  {['Male', 'Female', 'Prefer not to say'].map(gender => (
                    <Pressable
                      key={gender}
                      onPress={() => field.handleChange(gender)}
                      className={`px-4 py-3 rounded-full border ${
                        field.state.value === gender
                          ? 'border-[#00BFE0]'
                          : 'bg-background-50 border-background-300'
                      }`}
                      style={
                        field.state.value === gender
                          ? { backgroundColor: '#00BFE0' }
                          : {}
                      }
                    >
                      <HStack className="items-center" space="xs">
                        <Text
                          className={`font-dm-sans-medium ${
                            field.state.value === gender
                              ? 'text-white'
                              : 'text-typography-700'
                          }`}
                        >
                          {gender}
                        </Text>
                        {field.state.value === gender && (
                          <CheckCircle size={16} color="white" />
                        )}
                      </HStack>
                    </Pressable>
                  ))}
                </HStack>
              )}
            </form.Field>
          </VStack>

          {/* Trainer Selection */}
          <VStack space="md">
            <Text className="text-base font-dm-sans-medium text-typography-900">
              Is there a particular trainer you want to work with?
            </Text>

            <form.Field name="preferredTrainer">
              {field => (
                <HStack space="sm" className="flex-wrap">
                  {trainersLoading ? (
                    <Text className="text-sm text-typography-500">
                      Loading trainers...
                    </Text>
                  ) : (
                    <>
                      {/* Anyone option */}
                      <Pressable
                        key="anyone"
                        onPress={() => field.handleChange('anyone')}
                        className={`items-center ${
                          field.state.value === 'anyone'
                            ? 'opacity-100'
                            : 'opacity-70'
                        }`}
                      >
                        <VStack className="items-center" space="xs">
                          <VStack className="relative">
                            <Avatar
                              size="lg"
                              className={`border-2 ${
                                field.state.value === 'anyone'
                                  ? 'border-[#00BFE0]'
                                  : 'border-transparent'
                              }`}
                            >
                              <AvatarFallbackText className="bg-background-300 text-typography-700">
                                {getInitials('Anyone')}
                              </AvatarFallbackText>
                            </Avatar>
                            {field.state.value === 'anyone' && (
                              <VStack className="absolute -top-1 -right-1 bg-[#00BFE0] rounded-full p-1">
                                <CheckCircle size={16} color="white" />
                              </VStack>
                            )}
                          </VStack>
                          <Text
                            className={`text-xs font-dm-sans-medium ${
                              field.state.value === 'anyone'
                                ? 'text-[#00BFE0]'
                                : 'text-typography-700'
                            }`}
                          >
                            Anyone
                          </Text>
                        </VStack>
                      </Pressable>

                      {/* Real trainers from API */}
                      {trainers?.map(trainer => {
                        const trainerName = `${trainer.first_name} ${trainer.last_name}`;
                        const trainerId = trainer.id.toString();
                        return (
                          <Pressable
                            key={trainer.id}
                            onPress={() => field.handleChange(trainerId)}
                            className={`items-center ${
                              field.state.value === trainerId
                                ? 'opacity-100'
                                : 'opacity-70'
                            }`}
                          >
                            <VStack className="items-center" space="xs">
                              <VStack className="relative">
                                <Avatar
                                  size="lg"
                                  className={`border-2 ${
                                    field.state.value === trainerId
                                      ? 'border-[#00BFE0]'
                                      : 'border-transparent'
                                  }`}
                                >
                                  {trainer.profile_image ? (
                                    <AvatarImage
                                      source={{ uri: trainer.profile_image }}
                                      alt={trainerName}
                                    />
                                  ) : (
                                    <AvatarFallbackText className="bg-background-300 text-typography-700">
                                      {getInitials(trainerName)}
                                    </AvatarFallbackText>
                                  )}
                                </Avatar>
                                {field.state.value === trainerId && (
                                  <VStack className="absolute -top-1 -right-1 bg-[#00BFE0] rounded-full p-1">
                                    <CheckCircle size={16} color="white" />
                                  </VStack>
                                )}
                              </VStack>
                              <Text
                                className={`text-xs font-dm-sans-medium ${
                                  field.state.value === trainerId
                                    ? 'text-[#00BFE0]'
                                    : 'text-typography-700'
                                }`}
                              >
                                {trainer.first_name}
                              </Text>
                            </VStack>
                          </Pressable>
                        );
                      })}
                    </>
                  )}
                </HStack>
              )}
            </form.Field>
          </VStack>
        </VStack>
      </ScrollView>

      {/* Submit Button */}
      <VStack className="bg-background-0 px-4 py-4 border-t border-background-200">
        <Button
          variant="solid"
          size="lg"
          className="rounded-full"
          style={{ backgroundColor: '#00BFE0' }}
          onPress={() => form.handleSubmit()}
          disabled={submitRequestInfo.isPending}
        >
          <ButtonText className="text-white font-dm-sans-bold text-base">
            {submitRequestInfo.isPending ? 'Submitting...' : 'Submit'}
          </ButtonText>
        </Button>
      </VStack>
    </SafeAreaView>
  );
};

export default RequestInfo;
