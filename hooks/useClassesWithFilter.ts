import { useCallback, useMemo, useState } from 'react';
import { matchSorter } from 'match-sorter';
import { useClassesQuery } from '@/data/screens/classes/queries/useClassesQuery';
import { formatDate } from '@/data/common/common.utils';
import { ClassDetailsResponse } from '@/data/screens/classes/types';
import { CategoryType } from '@/data/screens/appointments/types';
import { useCategoriesAppointments } from '@/data/screens/appointments/queries/useCategoriesAppointments';
import { useFilter } from './useFilter';
import {
  FilterField,
  FilterValues,
} from '@/components/shared/filter-component';
import { useClientInfo } from '@/data/screens/common/queries/useClientConfig';
import { useTrainers } from '@/data/screens/trainers/queries/useTrainers';
import { useSession } from '@/modules/login/auth-provider';

interface TimeRange {
  startTime: string;
  endTime: string;
}

interface ClassesFilterParams {
  gym_id?: string;
  instructor_id?: string;
  class_type?: string[];
  start_time?: string;
  end_time?: string;
  only_available_reservations?: boolean;
}

const sanitizeClassFilters = (values: FilterValues): ClassesFilterParams => {
  const sanitized: ClassesFilterParams = {};

  // Handle gym_ids (location filter)
  if (
    values.gym_ids &&
    typeof values.gym_ids === 'string' &&
    values.gym_ids.trim()
  ) {
    sanitized.gym_id = values.gym_ids.trim();
  }

  // Handle instructor filter
  if (
    values.instructor_id &&
    typeof values.instructor_id === 'string' &&
    values.instructor_id.trim()
  ) {
    sanitized.instructor_id = values.instructor_id.trim();
  }

  // Handle class types
  if (values.types && Array.isArray(values.types) && values.types.length > 0) {
    sanitized.class_type = values.types.filter(
      type => typeof type === 'string' && type.trim()
    );
  }

  // Handle time range
  if (values.time && typeof values.time === 'object' && values.time !== null) {
    const timeRange = values.time as TimeRange;
    if (timeRange.startTime && timeRange.startTime !== '04:00') {
      sanitized.start_time = timeRange.startTime;
    }
    if (timeRange.endTime && timeRange.endTime !== '24:00') {
      sanitized.end_time = timeRange.endTime;
    }
  }

  // Handle availability toggle
  if (values.only_available_reservations === true) {
    sanitized.only_available_reservations = true;
  }

  return sanitized;
};

export const useClassesWithFilter = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTab, setSelectedTab] = useState<'classes' | 'appointment'>(
    'classes'
  );
  const [selectedDate, setSelectedDate] = useState(new Date());

  const [appliedFilters, setAppliedFilters] = useState<ClassesFilterParams>({});

  const { data: clientData } = useClientInfo();
  const { data: session } = useSession();

  // Fetch trainers for instructor filter
  const { data: trainersData = [] } = useTrainers('');

  // Build query params for classes
  const classQueryParams = useMemo(
    () => ({
      date: formatDate(selectedDate),
      ...appliedFilters,
    }),
    [selectedDate, appliedFilters]
  );

  // Queries
  const {
    data: classesData = [],
    isLoading: isClassesLoading,
    error: classesQueryError,
    refetch: refetchClasses,
    isRefetching: isClassesRefetching,
  } = useClassesQuery(classQueryParams);

  const {
    data: categoriesData,
    isLoading: isCategoriesLoading,
    error: categoriesQueryError,
    isRefetching: isCategoriesRefetching,
    refetch: refetchCategories,
  } = useCategoriesAppointments();

  // Filter fields (for classes tab)
  const filterFields: FilterField[] = useMemo(
    () => [
      {
        type: 'multiselect',
        key: 'types',
        label: 'Class type',
        options: [
          { label: 'Virtual', value: 'virtual' },
          { label: 'Live', value: 'live' },
          { label: 'Virtual & Live', value: 'virtual_live' },
        ],
      },
      {
        type: 'select',
        key: 'gym_ids',
        label: 'Location',
        placeholder: 'Select location',
        options:
          clientData?.facilities.map(f => ({
            label: f.name,
            value: String(f.id),
          })) || [],
      },
      {
        type: 'select',
        key: 'instructor_id',
        label: 'Instructor',
        placeholder: 'Select instructor',
        options:
          trainersData.map(trainer => ({
            label: `${trainer.first_name} ${trainer.last_name}`,
            value: String(trainer.id),
          })) || [],
      },
      {
        type: 'timeRange',
        key: 'time',
        label: 'Available start times',
        placeholder: 'Select time',
      },
      {
        type: 'toggle',
        key: 'only_available_reservations',
        label: 'Show only classes with available reservations',
      },
    ],
    [clientData?.facilities, trainersData]
  );

  // Initialize filter state
  const {
    filterValues,
    filterProps,
    hasActiveFilters,
    activeFilterCount,
    clearAllFilters: baseClearAllFilters,
  } = useFilter(filterFields, {
    onApply: (values: FilterValues) => {
      setAppliedFilters(sanitizeClassFilters(values));
    },
    onReset: () => {
      setAppliedFilters({});
    },
  });

  // Compute filtered data
  const filteredData = useMemo(() => {
    if (selectedTab === 'appointment') {
      // Only search term applies on categories
      const base = (categoriesData as CategoryType[]) ?? [];
      if (!searchTerm) return base;
      return matchSorter(base, searchTerm, {
        keys: ['name', 'gym_name', 'room_name', 'reservations_count'],
      });
    }

    let base = (classesData as ClassDetailsResponse[]) ?? [];

    // Apply search term
    if (searchTerm) {
      base = matchSorter(base, searchTerm, {
        keys: [
          'name',
          'room_name',
          'gym_name',
          'instructor_first_name',
          'instructor_last_name',
          'start_time',
        ],
      });
    }

    return base;
  }, [selectedTab, classesData, categoriesData, searchTerm]);

  const refetch = useCallback(() => {
    if (selectedTab === 'classes') return refetchClasses();
    return refetchCategories();
  }, [selectedTab, refetchClasses, refetchCategories]);

  // Handle tab change
  const handleTabChange = useCallback((tab: 'classes' | 'appointment') => {
    setSelectedTab(tab);
    setSearchTerm('');
  }, []);

  const handleDateChange = useCallback(
    (date: Date) => setSelectedDate(date),
    []
  );
  const clearSearch = useCallback(() => setSearchTerm(''), []);

  const clearAllFilters = useCallback(() => {
    setAppliedFilters({});
    baseClearAllFilters();
  }, [baseClearAllFilters]);

  const currentRecord = useMemo(() => {
    if (selectedTab === 'classes') {
      return {
        isLoading: isClassesLoading,
        isRefetching: isClassesRefetching,
        error: classesQueryError,
      };
    }
    return {
      isLoading: isCategoriesLoading,
      isRefetching: isCategoriesRefetching,
      error: categoriesQueryError,
    };
  }, [
    selectedTab,
    isClassesLoading,
    isClassesRefetching,
    classesQueryError,
    isCategoriesLoading,
    isCategoriesRefetching,
    categoriesQueryError,
  ]);

  // Expose a string of selected facility ids for consumers that need it
  const selectedFacility = appliedFilters.gym_id;

  return {
    // Data
    filteredData,
    ...currentRecord,

    // UI State
    selectedTab,
    selectedDate,
    searchTerm,

    // Actions
    setSearchTerm,
    clearSearch,
    handleTabChange,
    handleDateChange,
    refetch,

    // Filter API for UI
    filterProps,
    activeFilterCount,
    clearAllFilters,
    hasActiveFilters,

    // For navigation needs
    selectedFacility,
  };
};
